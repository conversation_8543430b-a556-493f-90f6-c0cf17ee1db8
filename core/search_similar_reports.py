#!/usr/bin/env python3
"""
Search Similar Reports System for Psychometrist Portal
Finds and ranks similar psychological reports based on patient characteristics and diagnoses
"""

import os
import json
import re
from datetime import datetime, timedelta
from pathlib import Path
from dotenv import load_dotenv
from .openai_client import setup_openai_client

# Load environment variables
load_dotenv()

def create_similarity_prompt(target_report, comparison_report):
    """
    Create a prompt for AI to calculate similarity between two reports
    """
    prompt = f"""
You are an expert clinical psychologist comparing two psychological reports for similarity.

TARGET REPORT:
{target_report}

COMPARISON REPORT:
{comparison_report}

Calculate similarity based on these factors and return a JSON response:

{{
    "overall_similarity": "Integer 0-100 representing overall similarity percentage",
    "demographic_match": "Integer 0-100 for age, gender similarity",
    "diagnosis_match": "Integer 0-100 for similar diagnoses/conditions",
    "symptom_match": "Integer 0-100 for similar presenting symptoms",
    "assessment_match": "Integer 0-100 for similar test types used",
    "treatment_match": "Integer 0-100 for similar treatment approaches",
    "key_similarities": [
        "List 2-3 key similarities between the cases"
    ],
    "useful_for": "Brief explanation of how this comparison case could help with the target case"
}}

SIMILARITY CRITERIA:
- Demographics: Age range (within 2-3 years), same gender
- Diagnoses: Same or related psychological conditions
- Symptoms: Similar presenting problems and behaviors
- Assessments: Same psychological tests used (WISC, BASC, etc.)
- Treatment: Similar therapeutic approaches and interventions

Return ONLY valid JSON, no other text.
"""
    return prompt

def calculate_similarity_with_ai(target_report, comparison_report, client, model):
    """
    Use AI to calculate similarity between two reports
    """
    try:
        prompt = create_similarity_prompt(target_report, comparison_report)
        
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,  # Low temperature for consistent scoring
            max_tokens=1000
        )
        
        ai_response = response.choices[0].message.content
        
        # Try to parse as JSON
        try:
            # First try direct parsing
            similarity = json.loads(ai_response)
            return similarity, "SUCCESS"
        except json.JSONDecodeError:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'(\{.*\})', ai_response, re.DOTALL)
            if json_match:
                try:
                    similarity = json.loads(json_match.group(1))
                    return similarity, "SUCCESS"
                except json.JSONDecodeError:
                    pass
            
            return {"error": "Could not parse AI response", "raw_response": ai_response}, "ERROR"
    
    except Exception as e:
        print("OpenAI API error in calculate_similarity_with_ai:", e)
        if hasattr(e, 'response'):
            print("OpenAI response:", getattr(e.response, 'text', e.response))
        return {"error": str(e)}, "ERROR"

def extract_report_metadata(report_content, report_filename):
    """
    Extract key metadata from a report for filtering
    """
    metadata = {
        "filename": report_filename,
        "age": None,
        "gender": None,
        "diagnoses": [],
        "date": None,
        "test_types": []
    }
    
    # Extract age
    age_match = re.search(r'(\d+)[-\s]year[-\s]old', report_content, re.IGNORECASE)
    if age_match:
        metadata["age"] = int(age_match.group(1))
    
    # Extract gender
    if re.search(r'\bmale\b', report_content, re.IGNORECASE):
        metadata["gender"] = "Male"
    elif re.search(r'\bfemale\b', report_content, re.IGNORECASE):
        metadata["gender"] = "Female"
    
    # Extract common diagnoses
    diagnoses_patterns = [
        r'anxiety', r'depression', r'ADHD', r'autism', r'PTSD',
        r'social anxiety', r'generalized anxiety', r'learning disorder'
    ]
    
    for pattern in diagnoses_patterns:
        if re.search(pattern, report_content, re.IGNORECASE):
            metadata["diagnoses"].append(pattern.replace(r'\b', '').replace('\\', ''))
    
    # Extract test types
    test_patterns = [
        r'WISC', r'WAIS', r'BASC', r'WIAT', r'Conners', r'MASC', r'CDI'
    ]
    
    for pattern in test_patterns:
        if re.search(pattern, report_content, re.IGNORECASE):
            metadata["test_types"].append(pattern.replace(r'\b', '').replace('\\', ''))
    
    # Extract date from filename if possible
    date_match = re.search(r'(\d{8})', report_filename)
    if date_match:
        try:
            date_str = date_match.group(1)
            metadata["date"] = datetime.strptime(date_str, "%Y%m%d").date()
        except:
            pass
    
    return metadata

def search_similar_reports(target_report_path, reports_directory="reports", filters=None):
    """
    Search for similar reports based on target report
    """
    print("🔍 SEARCH SIMILAR REPORTS SYSTEM")
    print("=" * 60)
    
    # Step 1: Read target report
    try:
        with open(target_report_path, 'r', encoding='utf-8') as f:
            target_content = f.read()
    except Exception as e:
        return {"error": f"Could not read target report: {e}"}
    
    target_filename = Path(target_report_path).name
    print(f"🎯 Target Report: {target_filename}")
    
    # Step 2: Find all comparison reports
    reports_path = Path(reports_directory)
    if not reports_path.exists():
        return {"error": f"Reports directory not found: {reports_directory}"}
    
    report_files = [f for f in reports_path.glob("*_psychological_report.txt") 
                   if f.name != target_filename]
    
    if not report_files:
        return {"error": "No comparison reports found"}
    
    print(f"📄 Found {len(report_files)} reports to compare")
    
    # Step 3: Set up AI
    print("🔧 Setting up OpenAI...")
    client, model, status = setup_openai_client()
    
    if status != "SUCCESS":
        return {"error": status}
    
    print(f"✅ OpenAI ready (Model: {model})")
    
    # Step 4: Extract target metadata for filtering
    target_metadata = extract_report_metadata(target_content, target_filename)
    print(f"🎯 Target: {target_metadata.get('age', 'Unknown')} y/o {target_metadata.get('gender', 'Unknown')}")
    
    # Step 5: Apply filters and calculate similarities
    print("🧠 Calculating similarities...")
    
    similarities = []
    
    for i, report_file in enumerate(report_files[:5], 1):  # Limit to 5 for demo
        print(f"   📋 Comparing with report {i}: {report_file.name}")
        
        # Read comparison report
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                comparison_content = f.read()
        except Exception as e:
            print(f"   ❌ Error reading {report_file.name}: {e}")
            continue
        
        # Extract metadata
        comparison_metadata = extract_report_metadata(comparison_content, report_file.name)
        
        # Apply filters if provided
        if filters and not passes_filters(comparison_metadata, filters):
            print(f"   ⏭️  Skipped (doesn't match filters)")
            continue
        
        # Calculate similarity with AI
        similarity, ai_status = calculate_similarity_with_ai(
            target_content[:2000],  # Limit length for faster processing
            comparison_content[:2000],
            client,
            model
        )
        
        if ai_status == "SUCCESS":
            similarity["metadata"] = comparison_metadata
            similarity["filename"] = report_file.name
            similarities.append(similarity)
            
            overall_sim = similarity.get("overall_similarity", 0)
            print(f"   ✅ Similarity: {overall_sim}%")
        else:
            print(f"   ❌ Failed to calculate similarity")
    
    # Step 6: Sort by similarity and return results
    similarities.sort(key=lambda x: int(x.get("overall_similarity", 0)), reverse=True)
    
    result = {
        "target_report": target_filename,
        "target_metadata": target_metadata,
        "similar_reports": similarities,
        "total_compared": len(similarities),
        "status": "SUCCESS"
    }
    
    return result

def passes_filters(metadata, filters):
    """
    Check if report metadata passes the specified filters
    """
    if not filters:
        return True
    
    # Age filter
    if "age_min" in filters and metadata.get("age"):
        if metadata["age"] < filters["age_min"]:
            return False
    
    if "age_max" in filters and metadata.get("age"):
        if metadata["age"] > filters["age_max"]:
            return False
    
    # Gender filter
    if "gender" in filters and filters["gender"] != "All":
        if metadata.get("gender") != filters["gender"]:
            return False
    
    # Date range filter
    if "date_from" in filters and metadata.get("date"):
        if metadata["date"] < filters["date_from"]:
            return False
    
    return True

def display_search_results(results):
    """
    Display search results in wireframe format
    """
    print("\n" + "=" * 80)
    print("🔍 SIMILAR REPORTS SEARCH RESULTS")
    print("=" * 80)
    
    target = results["target_metadata"]
    print(f"🎯 Target: {target.get('age', 'Unknown')} y/o {target.get('gender', 'Unknown')}")
    print(f"📄 Compared with {results['total_compared']} reports")
    print()
    
    similar_reports = results["similar_reports"]
    
    if not similar_reports:
        print("❌ No similar reports found")
        return
    
    print("📊 SIMILAR REPORTS FOUND:")
    print("-" * 50)
    
    for i, report in enumerate(similar_reports[:5], 1):  # Show top 5
        metadata = report.get("metadata", {})
        similarity = report.get("overall_similarity", 0)
        
        print(f"{i}. {report['filename']} - {similarity}% match")
        print(f"   👤 {metadata.get('age', 'Unknown')} y/o {metadata.get('gender', 'Unknown')}")
        
        if metadata.get("diagnoses"):
            print(f"   🏥 Diagnoses: {', '.join(metadata['diagnoses'])}")
        
        if report.get("key_similarities"):
            print(f"   🔗 Similarities: {', '.join(report['key_similarities'])}")
        
        if report.get("useful_for"):
            print(f"   💡 Useful for: {report['useful_for']}")
        
        print()
    
    print("=" * 80)

def main():
    """
    Test the search similar reports system
    """
    print("🚀 TESTING SEARCH SIMILAR REPORTS SYSTEM")
    print("=" * 80)
    
    # Use the most recent report as target
    reports_path = Path("reports")
    report_files = list(reports_path.glob("*_psychological_report.txt"))
    
    if not report_files:
        print("❌ No reports found for testing")
        return
    
    # Use the newest report as target
    target_report = max(report_files, key=lambda f: f.stat().st_mtime)
    
    # Example filters (like in wireframe)
    filters = {
        "age_min": 10,
        "age_max": 18,
        "gender": "All",  # "Male", "Female", or "All"
        "date_from": datetime.now().date() - timedelta(days=365)  # Last 12 months
    }
    
    result = search_similar_reports(str(target_report), filters=filters)
    
    if "error" in result:
        print(f"❌ ERROR: {result['error']}")
    else:
        display_search_results(result)
        
        # Save results
        output_file = f"similar_reports_search_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, default=str)
        
        print(f"💾 Search results saved to: {output_file}")
    
    print("\n🎉 SEARCH SIMILAR REPORTS TEST FINISHED!")

if __name__ == "__main__":
    main()
