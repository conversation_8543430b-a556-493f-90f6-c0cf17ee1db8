import os
import json
from dotenv import load_dotenv
from .document_reader import read_document

# Load environment variables
load_dotenv()

def setup_groq_client():
    """
    Set up the Groq client with API key from .env
    """
    try:
        from groq import Groq

        api_key = os.getenv('GROQ_API_KEY2')
        model = os.getenv('MODEL1')

        if not api_key:
            return None, None, "ERROR: GROQ_API_KEY not found in .env file"

        if not model:
            return None, None, "ERROR: MODEL not found in .env file"

        client = Groq(api_key=api_key)
        return client, model, "SUCCESS"

    except ImportError:
        return None, None, "ERROR: groq library not installed. Run: pip install groq"
    except Exception as e:
        return None, None, f"ERROR setting up Groq: {str(e)}"
