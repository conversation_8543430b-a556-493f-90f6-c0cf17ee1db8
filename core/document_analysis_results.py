#!/usr/bin/env python3
"""
Document Analysis Results System for Psychometrist Portal
Analyzes all documents in a patient folder and creates results dashboard
"""

import os
import json
from pathlib import Path
from dotenv import load_dotenv
from .document_reader import read_document
from .openai_client import setup_openai_client

# Load environment variables
load_dotenv()

def create_classification_prompt(document_text, filename):
    """
    Create a prompt for AI to classify and analyze the document
    """
    prompt = f"""
You are an expert psychologist analyzing a psychological assessment document.

DOCUMENT FILENAME: {filename}
DOCUMENT TEXT: {document_text}

Classify this document and provide analysis in JSON format:

{{
    "category": "Choose ONE: Cognitive Tests, Academic Tests, Inventories, Behavioral Observation Scales, Background Info, Forms/Consent, Communication Logs, or Other",
    "test_name": "Specific test name (e.g., 'WISC-V Intelligence Test', 'BASC-3 Parent Rating Scales', 'Initial Interview Notes')",
    "file_type": "Type of document (e.g., 'Score Report', 'Raw Data', 'Observation Form', 'Interview Notes', 'Consent Form')",
    "completeness": "Full Report, Partial Data, Raw Scores Only, or Incomplete",
    "confidence": "Integer 1-100 representing confidence in this classification",
    "key_info": "Brief description of what this document contains",
    "clinical_value": "High, Medium, or Low - how valuable is this for psychological assessment"
}}

CLASSIFICATION GUIDELINES:
- Cognitive Tests: WISC, WAIS, WPPSI, Stanford-Binet, etc.
- Academic Tests: WIAT, WJ (Woodcock-Johnson), KTEA, etc.
- Inventories: BASC, Conners, CBCL, Beck inventories, etc.
- Behavioral Observation: Classroom observations, behavioral checklists
- Background Info: Interview notes, history forms, referral information
- Forms/Consent: Legal documents, consent forms, policies
- Communication Logs: Messages, emails, appointment logs

Return ONLY valid JSON, no other text.
"""
    return prompt

def classify_document_with_ai(document_text, filename, client, model):
    """
    Use AI to classify and analyze a single document
    """
    try:
        prompt = create_classification_prompt(document_text, filename)
        
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,  # Low temperature for consistent classification
            max_tokens=500
        )
        
        ai_response = response.choices[0].message.content
        
        # Try to parse as JSON
        try:
            # First try direct parsing
            classification = json.loads(ai_response)
            return classification, "SUCCESS"
        except json.JSONDecodeError:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'(\{.*\})', ai_response, re.DOTALL)
            if json_match:
                try:
                    classification = json.loads(json_match.group(1))
                    return classification, "SUCCESS"
                except json.JSONDecodeError:
                    pass
            
            return {"error": "Could not parse AI response", "raw_response": ai_response}, "ERROR"
    
    except Exception as e:
        print("OpenAI API error in classify_document_with_ai:", e)
        if hasattr(e, 'response'):
            print("OpenAI response:", getattr(e.response, 'text', e.response))
        return {"error": str(e)}, "ERROR"

def analyze_patient_documents(patient_folder_path):
    """
    Analyze all documents in a patient folder
    """
    print(f"📁 DOCUMENT ANALYSIS RESULTS SYSTEM")
    print("=" * 60)
    
    # Step 1: Find all documents
    patient_path = Path(patient_folder_path)
    if not patient_path.exists():
        return {"error": f"Patient folder not found: {patient_folder_path}"}
    
    # Look for documents in the documents subfolder
    documents_path = patient_path / "documents"
    if not documents_path.exists():
        return {"error": f"Documents folder not found: {documents_path}"}
    
    # Get all document files
    document_files = []
    for ext in ['*.pdf', '*.docx', '*.doc']:
        document_files.extend(documents_path.glob(ext))
    
    if not document_files:
        return {"error": "No documents found in folder"}
    
    print(f"📄 Step 1: Found {len(document_files)} documents to analyze")
    
    # Step 2: Set up AI
    print("�� Step 2: Setting up OpenAI...")
    client, model, status = setup_openai_client()
    
    if status != "SUCCESS":
        return {"error": status}
    
    print(f"✅ OpenAI ready (Model: {model})")
    
    # Step 3: Analyze each document
    print("🧠 Step 3: Analyzing documents with AI...")
    
    results = {
        "patient_folder": str(patient_path.name),
        "total_documents": len(document_files),
        "analyses": [],
        "categories": {
            "Cognitive Tests": [],
            "Academic Tests": [],
            "Inventories": [],
            "Behavioral Observation Scales": [],
            "Background Info": [],
            "Forms/Consent": [],
            "Communication Logs": [],
            "Other": []
        },
        "summary": {}
    }
    
    for i, doc_file in enumerate(document_files, 1):
        print(f"   📋 Analyzing {i}/{len(document_files)}: {doc_file.name}")
        
        # Read document
        document_text = read_document(str(doc_file))
        
        if document_text.startswith("ERROR"):
            analysis = {
                "filename": doc_file.name,
                "error": document_text,
                "status": "ERROR"
            }
        else:
            # Classify with AI
            classification, ai_status = classify_document_with_ai(
                document_text[:3000],  # Limit text length for faster processing
                doc_file.name,
                client,
                model
            )
            
            analysis = {
                "filename": doc_file.name,
                "filepath": str(doc_file),
                "classification": classification,
                "status": ai_status
            }
        
        results["analyses"].append(analysis)
        
        # Add to category if successful
        if analysis.get("status") == "SUCCESS" and "classification" in analysis:
            category = analysis["classification"].get("category", "Other")
            if category in results["categories"]:
                results["categories"][category].append(analysis)
            else:
                results["categories"]["Other"].append(analysis)
    
    # Step 4: Create summary
    print("📊 Step 4: Creating summary...")
    
    successful_analyses = [a for a in results["analyses"] if a.get("status") == "SUCCESS"]
    results["summary"] = {
        "total_documents": len(document_files),
        "successful_analyses": len(successful_analyses),
        "failed_analyses": len(document_files) - len(successful_analyses),
        "categories_found": {cat: len(docs) for cat, docs in results["categories"].items() if docs},
        "average_confidence": calculate_average_confidence(successful_analyses)
    }
    
    print(f"✅ Analysis complete! {len(successful_analyses)}/{len(document_files)} documents analyzed successfully")
    
    return results

def calculate_average_confidence(analyses):
    """
    Calculate average confidence score across all analyses
    """
    confidences = []
    for analysis in analyses:
        if "classification" in analysis and "confidence" in analysis["classification"]:
            try:
                confidence = int(analysis["classification"]["confidence"])
                confidences.append(confidence)
            except (ValueError, TypeError):
                pass
    
    if confidences:
        return round(sum(confidences) / len(confidences), 1)
    return 0

def display_results_dashboard(results):
    """
    Display results in a format similar to the wireframe
    """
    print("\n" + "=" * 80)
    print("📊 DOCUMENT ANALYSIS RESULTS DASHBOARD")
    print("=" * 80)
    
    print(f"Patient: {results['patient_folder']}")
    print(f"Documents Analyzed: {results['summary']['successful_analyses']}/{results['summary']['total_documents']}")
    print(f"Average Confidence: {results['summary']['average_confidence']}%")
    print()
    
    # Display by category (like wireframe)
    for category, documents in results["categories"].items():
        if documents:  # Only show categories with documents
            print(f"📋 {category}")
            print("-" * 40)
            
            for doc in documents:
                if "classification" in doc:
                    classification = doc["classification"]
                    confidence = classification.get("confidence", "N/A")
                    test_name = classification.get("test_name", "Unknown")
                    file_type = classification.get("file_type", "")
                    
                    print(f"   {test_name} - {file_type}")
                    print(f"   File: {doc['filename']}")
                    print(f"   📊 {confidence}% Match")
                    print()
            
            print()
    
    print("🚀 Ready to Generate AI Report >")
    print("=" * 80)

def main():
    """
    Test the document analysis results system
    """
    # Test with Viktor's patient folder
    test_folder = "OWL Downloads/1148_VGo_2019-11-02_2025-05-02"
    
    print("🚀 TESTING DOCUMENT ANALYSIS RESULTS SYSTEM")
    print("=" * 80)
    
    results = analyze_patient_documents(test_folder)
    
    if "error" in results:
        print(f"❌ ERROR: {results['error']}")
    else:
        # Display dashboard
        display_results_dashboard(results)
        
        # Save results to file
        output_file = f"analysis_results_{results['patient_folder']}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"💾 Results saved to: {output_file}")
    
    print("\n✅ Test completed!")

if __name__ == "__main__":
    main()
