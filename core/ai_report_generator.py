#!/usr/bin/env python3
"""
AI Report Generator for Psychometrist Portal
Uses OpenAI API to generate comprehensive psychological reports matching professional standards
"""
import os
import json
from dotenv import load_dotenv
from .openai_client import setup_openai_client
import time
from datetime import datetime
import re

# Load environment variables
load_dotenv()

def load_sample_reports():
    """
    Load sample reports from the sample_reports directory to use as reference.
    """
    sample_reports = []
    sample_dir = "sample_reports"
    
    if not os.path.exists(sample_dir):
        print(f"Warning: {sample_dir} directory not found. Sample reports will not be used.")
        return sample_reports
    
    # Get all .txt files in the sample_reports directory
    txt_files = [f for f in os.listdir(sample_dir) if f.endswith('.txt')]
    
    for filename in txt_files:
        try:
            filepath = os.path.join(sample_dir, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                sample_reports.append({
                    'filename': filename,
                    'content': content
                })
            print(f"Loaded sample report: {filename}")
        except Exception as e:
            print(f"Error loading {filename}: {e}")
    
    return sample_reports

def create_section_prompt(patient_analyses, section, subsections=None, sample_reports=None):
    """
    Create a prompt for an entire section, listing all its subsections.
    """
    analyses_text = ""
    for i, analysis in enumerate(patient_analyses, 1):
        analyses_text += f"\n--- DOCUMENT {i}: {analysis.get('document_type', 'Unknown')} ---"
        analyses_text += json.dumps(analysis, indent=2)
        analyses_text += "\n"

    section_headings = {
        "front_page": "PSYCHOLOGICAL REPORT (Private and Confidential)",
        "reason_referral": "REASON FOR REFERRAL",
        "background": "BACKGROUND INFORMATION",
        "assessment_methods": "ASSESSMENT METHODS",
        "behavioral_observations": "BEHAVIORAL OBSERVATIONS",
        "assessment_results": "ASSESSMENT RESULTS",
        "clinical_impressions": "CLINICAL IMPRESSIONS AND DIAGNOSES",
        "recommendations": "RECOMMENDATIONS"
    }

    if section == "front_page":
        prompt = f"""
Generate the front page of the psychological report as a single HTML table. Your output MUST be ONLY a single <table>...</table> element, with no text, headings, explanations, or narrative before or after. Do NOT include any <html>, <body>, <h1>, <h2>, <p>, or any other tags. Do NOT explain, summarize, or add any content outside the table. If a value is missing, leave the cell blank. If you output anything except a single <table>...</table>, the result will be rejected.

The table should have the following rows (in this order):
- Name
- D.O.B
- Parents
- Address
- Phone
- School
- School Board
- Grade/Program
- Assessment by
- Assessment Dates
- Age at testing (age in years and months)

Use the following analyzed documents to fill in the information:
{analyses_text}
"""
        return prompt

    # For all other sections, list all subsections and instruct the AI to generate each as a separate HTML block
    if subsections:
        subsections_list = "\n".join([
            f"- {sub[0]} (at least {sub[1]} words): {sub[2]}" if len(sub) > 2 else f"- {sub[0]} (at least {sub[1]} words)" for sub in subsections
        ])
        
        # Add sample reports as reference if available
        sample_reference = ""
        if sample_reports:
            sample_reference = "\n\nREFERENCE SAMPLE REPORTS:\n"
            sample_reference += "Use these sample reports as reference for the structure, style, level of detail, and professional tone you should use:\n\n"
            for i, sample in enumerate(sample_reports[:2], 1):  # Use first 2 samples
                sample_reference += f"--- SAMPLE REPORT {i}: {sample['filename']} ---\n"
                sample_reference += sample['content'][:100000] + "...\n\n"  # First 100000 chars of each sample
        
        prompt = f"""
Generate the following subsections of the {section_headings.get(section, section)} section of a psychological report. For each subsection, generate a separate HTML block, wrapped in <html>...</html>, and clearly label each subsection with its subheading as an <h2> tag at the start of the block. Do NOT repeat the main section heading. Do NOT include any text outside the HTML tags. Do NOT use markdown.

Subsections to generate:
{subsections_list}

{sample_reference}

Formatting requirements:
- Write in clear, professional, and specific language.
- Avoid generic or repetitive statements.
- Use valid HTML for formatting:
  - Use <p> for paragraphs.
  - Use <ul> or <ol> and <li> for lists (especially for recommendations or multiple points).
  - Use <br> for line breaks if needed.
- CRITICAL: Each subsection must be at least the specified word count (not including HTML tags). Do not stop early - ensure you reach the minimum word count.
- Do not include any text outside the HTML tags.
- Do not use markdown.
- Be thorough and detailed in your responses to meet the word count requirements.
- Follow the professional style and level of detail shown in the sample reports above.

Use the following analyzed documents to fill in the information:
{analyses_text}
"""
        return prompt
    return ""

# Helper to strip main heading from subsection output
def strip_main_heading(text, main_heading):
    # Remove lines that match the main heading (case-insensitive, with or without markdown/HTML)
    pattern = re.compile(rf"^(#*\s*)?{re.escape(main_heading)}:?\s*$", re.IGNORECASE | re.MULTILINE)
    return pattern.sub("", text).lstrip("\n")

def generate_section(client, model, section_prompt, max_retries=5):
    """
    Generate a section of the report with retry logic
    """
    retry_delay = 120  # seconds (start at 2 minutes)
    for attempt in range(max_retries):
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": section_prompt}
                ],
                temperature=0.3,
                max_tokens=25000,  # Increased to allow longer responses
                stop=None
            )
            return response.choices[0].message.content
        except Exception as e:
            print("OpenAI API error in generate_section:", e)
            if hasattr(e, 'response'):
                print("OpenAI response:", getattr(e.response, 'text', e.response))
            if "429" in str(e) and attempt < max_retries - 1:
                print(f"[429 ERROR] Rate limit hit, retrying in {retry_delay} seconds (attempt {attempt+1}/{max_retries})...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                raise e

def save_report_preview(preview_data, output_path="report_preview.json"):
    """
    Save the current report preview to a JSON file.
    """
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(preview_data, f, ensure_ascii=False, indent=2)

def save_final_report_json(report_data, patient_name, output_dir="reports"):
    """
    Save the final report as a structured JSON file.
    """
    import os
    from datetime import datetime
    os.makedirs(output_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d")
    safe_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    filename = f"Psychological_Report_{safe_name}_{timestamp}.json"
    filepath = os.path.join(output_dir, filename)
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, ensure_ascii=False, indent=2)
    return filepath

def extract_first_html_table(text):
    """
    Extract only the first <table>...</table> block from the text. Returns the table HTML or an empty string if not found.
    """
    import re
    match = re.search(r"<table[\s\S]*?</table>", text, re.IGNORECASE)
    return match.group(0) if match else ""

def generate_report_with_ai(patient_analyses, client, model, s3_manager=None, s3_folder='reports', patient_id=None):
    import time
    import uuid
    try:
        # Load sample reports for reference
        print("Loading sample reports for reference...")
        sample_reports = load_sample_reports()
        if sample_reports:
            print(f"Loaded {len(sample_reports)} sample reports for reference")
        else:
            print("No sample reports found - proceeding without reference samples")
        sections_structure = {
            "front_page": {
                "subsections": [
                    ("Front Page", 100, "Generate a summary table with the client's identifying and assessment information.")
                ]
            },
            "reason_referral": {
                "subsections": [
                    ("Reason for Referral", 80, "Explain who referred the client, the reason for the referral, and the key concerns that prompted the psychoeducational assessment.")
                ]
            },
            "background": {
                "subsections": [
                    ("Sources of Background Information", 100, "List all background sources used, including interviews, forms, questionnaires, report cards, and previous assessments."),
                    ("Note", 100, "Mention missing or incomplete background information that could impact interpretation of results."),
                    ("Background Information", 140, "Summarize the client's demographic and personal profile, including age, gender, language, school, home situation, interests, and goals."),
                    ("Family Constellation", 150, "Describe the client's family structure, including parents’ occupations, education, and any relevant family history."),
                    ("Developmental and Medical History", 550, "Provide details about birth, developmental milestones, health status, and medical conditions"),
                    ("Academic History", 700, "Present a chronological summary of the client’s schooling, report card comments, academic performance, and any notable changes"),
                    ("Current Academic Functioning", 300, "Summarize the client’s current academic strengths, difficulties, and learning profile as observed or reported."),
                    ("Presenting Concerns", 300, "Summarize academic, behavioral, and emotional concerns as reported by teachers, parents, and the client.")
                ]
            },
            "assessment_methods": {
                "subsections": [
                    ("Tests Administered", 100, "List all standardized and informal assessment tools used, including cognitive, academic, and behavioral measures.")
                ]
            },
            "behavioral_observations": {
                "subsections": [
                    ("Behavioral Observations", 700, "Describe the client's behavior, communication, motivation, and engagement throughout the assessment process.")
                ]
            },
            "assessment_results": {
                "subsections": [
                    ("Percentile Ranges and Descriptors", 200, "Provide an introduction explaining how percentiles work and include a table showing percentile ranges for verbal descriptors used throughout the report (e.g., exceptionally low, below average, average, high average, above average, exceptionally high)."),
                    ("Academic Skills", 1000, "Summarize reading, writing, oral language, and math performance using standardized test data."),
                    ("Executive Functioning Skills", 1000, "Report on planning, organization, impulse control, and self-monitoring skills."),
                    ("Cognitive Abilities", 1000, "Summarize overall intellectual ability (e.g., IQ) and breakdown of cognitive domains."),
                    ("Attention, Concentration and Working Memory", 1000, "Describe performance on tasks involving focus, sustained attention, and short-term memory."),
                    ("Memory", 1000, "Summarize both verbal and visual memory abilities and test outcomes."),
                    ("Processing Speed", 1000, "Describe the client's mental efficiency and speed of completing cognitive tasks."),
                    ("Behavioral and Psychosocial Functioning", 1000, "Summarize findings from parent, teacher, and self-report tools on behavior, mood, and social-emotional well-being.")
                ]
            },
            "clinical_impressions": {
                "subsections": [
                    ("Summary of Key Findings", 400, "Provide a high-level summary of major patterns, strengths, and challenges identified in the assessment."),
                    ("Integration of Assessment Data", 400, "Combine all findings (background, test results, reports) to form a coherent picture of the client's functioning."),
                    ("Diagnostic Conclusions", 400, "State any relevant diagnoses and support them with clear evidence from the assessment."),
                    ("Impact on Functioning", 400, "Explain how the client’s strengths and difficulties affect daily life, school performance, and social interactions.")
                ]
            },
            "recommendations": {
                "subsections": [
                    ("Medical/Clinical Recommendations", 3000, "Offer practical, tailored recommendations in areas such as educational accommodations, therapy, home support, and community resources.")
                ]
            }
        }
        section_order = list(sections_structure.keys())
        full_report = []
        report_preview = {}
        section_headings = {
            "front_page": "PSYCHOLOGICAL REPORT (Private and Confidential)",
            "reason_referral": "REASON FOR REFERRAL",
            "background": "BACKGROUND INFORMATION",
            "assessment_methods": "ASSESSMENT METHODS",
            "behavioral_observations": "BEHAVIORAL OBSERVATIONS",
            "assessment_results": "ASSESSMENT RESULTS",
            "clinical_impressions": "CLINICAL IMPRESSIONS AND DIAGNOSES",
            "recommendations": "RECOMMENDATIONS"
        }
        for section_idx, section in enumerate(section_order):
            print(f"\n=== Generating section: {section} ===")
            subsections = sections_structure[section]["subsections"]
            prompt = create_section_prompt(patient_analyses, section, subsections=subsections, sample_reports=sample_reports)
            section_content = generate_section(client, model, prompt, max_retries=5)
            # For front page, extract only the first <table>...</table>
            if section == "front_page":
                section_content = extract_first_html_table(section_content)
                section_content = f"<html>{section_content}</html>"
                report_preview[section] = {subsections[0][0]: section_content}
                full_report.append(section_content)
            else:
                # Parse the AI output into subsections
                # Expecting each subsection as <html><h2>Subheading</h2>...</html>
                subsection_blocks = re.findall(r"<html>[\s\S]*?<\/html>", section_content, re.IGNORECASE)
                for i, (subheading, _, description) in enumerate(subsections):
                    if i < len(subsection_blocks):
                        report_preview.setdefault(section, {})[subheading] = subsection_blocks[i]
                        full_report.append(subsection_blocks[i])
                    else:
                        # If missing, leave blank
                        report_preview.setdefault(section, {})[subheading] = f"<html><h2>{subheading}</h2><p></p></html>"
                        full_report.append(f"<html><h2>{subheading}</h2><p></p></html>")
            save_report_preview(report_preview)
            # Wait 3 seconds after each section except the last one
            if section_idx < len(section_order) - 1:
                print("Waiting 3 seconds before generating the next section to avoid rate limits...")
                time.sleep(3)
        complete_report = "\n".join(full_report)
        # Try to get patient name for filename
        patient_name = "Unknown_Patient"
        if patient_analyses and isinstance(patient_analyses, list):
            for analysis in patient_analyses:
                if isinstance(analysis, dict) and "patient_info" in analysis and "name" in analysis["patient_info"]:
                    patient_name = analysis["patient_info"]["name"]
                    break
        # Save the final structured JSON report to S3
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        json_filename = f"{safe_name}_{timestamp}_psychological_report.json"
        if s3_manager:
            s3_upload_result = s3_manager.upload_json_data(
                data=report_preview,
                filename=json_filename,
                folder=s3_folder.rstrip('/'),
                metadata={
                    'patient-id': patient_id or '',
                    'patient-name': patient_name,
                    'content-type': 'application/json'
                }
            )
            json_s3_key = s3_upload_result["s3_key"]
        else:
            json_s3_key = None
        return complete_report, "SUCCESS", json_s3_key
    except Exception as e:
        return f"ERROR generating report: {str(e)}", "ERROR", None

def generate_psychological_report(patient_analyses, s3_manager=None, s3_folder='reports', patient_id=None):
    """
    Main function to generate a psychological report from multiple analyses
    """
    print(f"ð  AI PSYCHOLOGICAL REPORT GENERATOR")
    print("=" * 60)
    # Step 1: Validate input
    if not patient_analyses or len(patient_analyses) == 0:
        return {"error": "No patient analyses provided"}
    print(f"ð  Processing {len(patient_analyses)} document analyses...")
    # Step 2: Set up AI
    print("ð § Setting up OpenAI...")
    client, model, status = setup_openai_client()
    if status != "SUCCESS":
        return {"error": status}
    print(f"â  OpenAI ready (Model: {model})")
    # Step 3: Generate report with AI
    print("ð §  Generating comprehensive psychological report...")
    report, ai_status, final_json_path = generate_report_with_ai(
        patient_analyses, client, model, s3_manager=s3_manager, s3_folder=s3_folder, patient_id=patient_id
    )
    print(f"â  Report generation complete (Status: {ai_status})")
    if ai_status == "SUCCESS":
        return {"report": report, "status": "SUCCESS", "json_s3_key": final_json_path}
    else:
        return {"error": report, "status": ai_status, "json_s3_key": final_json_path}

def save_report_to_file(report_content, patient_name, output_dir="reports"):
    """
    Save the generated report to a file with professional naming
    """
    import os
    from datetime import datetime
    # Create reports directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    # Create filename with patient name and timestamp
    timestamp = datetime.now().strftime("%Y%m%d")
    safe_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    filename = f"Psychological_Report_{safe_name}_{timestamp}.docx"
    filepath = os.path.join(output_dir, filename)
    # Save report
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(report_content)
    return filepath

def main():
    """
    Test the report generator with sample analyses
    """
    # Sample analysis data matching real psychological assessments
    sample_analyses = [
        {
            "document_type": "WISC-V Canadian Score Report",
            "patient_info": {
                "name": "Ken Wilson",
                "age": "8 years 5 months",
                "date_of_birth": "2014/03/15",
                "gender": "Male",
                "address": "123 Main Avenue, Ottawa, ON K1A 0B2",
                "phone": "************ (mom), ************ (dad)",
                "school": "Elmdale Public School",
                "school_board": "Ottawa Carleton District School Board",
                "grade": "Grade 3, Early French Immersion"
            },
            "assessment_details": {
                "psychologist": "Dr. Deanna Drahovzal",
                "psychometrist": "Ms. Smith",
                "assessment_dates": [
                    "July 21, 2022 (Intake Interview)",
                    "August 18 & 25, 2022 (Testing)",
                    "September 6, 2022 (Clinical Interview)"
                ]
            },
            "referral_info": {
                "referrer": "pediatrician",
                "referral_date": "May 2, 2022",
                "reason": "recently diagnosed with ADHD by pediatrician, purpose is to rule out other possible diagnoses and address struggles with impulsivity, self-regulation, and anxiety"
            },
            "test_results": {
                "WISC-V": {
                    "Full_Scale_IQ": "High Average range (84th percentile)",
                    "GAI": "Above Average range (95th percentile)",
                    "VCI": "Above Average range (95th percentile)",
                    "VSI": "Exceptionally High range (99th percentile)",
                    "FRI": "High Average range (79th percentile)",
                    "WMI": "Average range (50th percentile)",
                    "PSI": "Low Average range (23rd percentile)"
                },
                "WIAT-III": {
                    "Oral_Language": {
                        "Expressive_Vocabulary": "Exceptionally High range (99th percentile)",
                        "Listening_Comprehension": "Exceptionally High range (98th percentile)"
                    },
                    "Reading": {
                        "Decoding": "Average range (66th percentile)",
                        "Comprehension": "Above Average range (92nd percentile)"
                    },
                    "Writing": {
                        "Spelling": "Average range (58th percentile)",
                        "Essay_Composition": "Low Average range (10th percentile)"
                    },
                    "Math": {
                        "Reasoning": "Above Average range (92nd percentile)",
                        "Calculation": "Average range (55th percentile)"
                    }
                }
            },
            "behavioral_observations": {
                "appearance": "reserved initially but established rapport",
                "behavior": "often appeared annoyed, distracted, and disinterested",
                "attention": "low tolerance for frustrating questions, did not persevere",
                "social": "struggled with reciprocal interactions, gave short answers"
            },
            "diagnostic_impressions": [
                "Attention Deficit Hyperactivity Disorder - Combined Type (ADHD:C)",
                "High Ability Learner"
            ],
            "confidence_level": "High"
        }
    ]
    print("ð � � �  TESTING PSYCHOLOGICAL REPORT GENERATOR")
    print("=" * 60)
    result = generate_psychological_report(sample_analyses)
    if result.get("status") == "SUCCESS":
        print("ð � � �  GENERATED REPORT:")
        print("=" * 60)
        print(result["report"][:1000] + "...")  # Print first 1000 chars to avoid overwhelming output
        # Save report to file
        patient_name = sample_analyses[0]["patient_info"]["name"]
        filepath = save_report_to_file(result["report"], patient_name)
        print(f"ð � � ¾ Professional report saved to: {filepath}")
    else:
        print(f"â � �  ERROR: {result.get('error')}")
    print("\n" + "=" * 60)
    print("â � �  Test completed!")

if __name__ == "__main__":
    main()