#!/usr/bin/env python3
"""
Test script for S3 cleanup endpoints
Demonstrates folder deletion and complete cleanup functionality
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test API health"""
    print("🔍 Testing API Health...")
    response = requests.get(f"{BASE_URL}/health")
    
    if response.status_code == 200:
        health = response.json()
        print(f"✅ API Status: {health['status']}")
        print(f"☁️  S3 System: {health['s3_system']['status']}")
        return True
    else:
        print(f"❌ Health check failed: {response.status_code}")
        return False

def show_s3_status():
    """Show current S3 folder status"""
    print("\n📊 Current S3 Status:")
    folders = ["uploads", "reports", "cheatsheets", "analysis_results", "temp"]
    
    total_files = 0
    for folder in folders:
        try:
            response = requests.get(f"{BASE_URL}/s3/files?folder={folder}")
            if response.status_code == 200:
                data = response.json()
                file_count = data['total_files']
                total_files += file_count
                print(f"   📂 {folder}: {file_count} files")
            else:
                print(f"   ❌ {folder}: Error getting status")
        except Exception as e:
            print(f"   ❌ {folder}: {e}")
    
    print(f"📊 Total files across all folders: {total_files}")
    return total_files

def test_folder_deletion_safety():
    """Test the safety checks for folder deletion"""
    print("\n🛡️  Testing Safety Checks...")
    
    # Test 1: Try to delete without confirmation
    print("🧪 Test 1: Delete without confirmation (should fail)")
    response = requests.delete(f"{BASE_URL}/s3/folders/uploads")
    
    if response.status_code == 400:
        print("✅ Safety check working - requires confirmation")
        print(f"   Message: {response.json()['detail']}")
    else:
        print(f"❌ Safety check failed: {response.status_code}")
    
    # Test 2: Try to delete invalid folder
    print("\n🧪 Test 2: Delete invalid folder (should fail)")
    response = requests.delete(f"{BASE_URL}/s3/folders/invalid_folder?confirm=true")
    
    if response.status_code == 400:
        print("✅ Folder validation working")
        print(f"   Message: {response.json()['detail']}")
    else:
        print(f"❌ Folder validation failed: {response.status_code}")

def test_folder_deletion(folder="temp"):
    """Test deleting a specific folder"""
    print(f"\n🗑️  Testing Folder Deletion: {folder}")
    
    # First check what's in the folder
    response = requests.get(f"{BASE_URL}/s3/files?folder={folder}")
    if response.status_code == 200:
        data = response.json()
        file_count = data['total_files']
        print(f"📄 Found {file_count} files in '{folder}' folder")
        
        if file_count == 0:
            print("📂 Folder is already empty")
        else:
            print("📋 Files to be deleted:")
            for i, file_info in enumerate(data['files'][:5], 1):  # Show first 5
                print(f"   {i}. {file_info['filename']} ({file_info['size']} bytes)")
            if len(data['files']) > 5:
                print(f"   ... and {len(data['files']) - 5} more files")
    
    # Delete the folder
    print(f"\n🗑️  Deleting all files in '{folder}' folder...")
    response = requests.delete(f"{BASE_URL}/s3/folders/{folder}?confirm=true")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Folder deletion completed!")
        print(f"📊 Files found: {result['files_found']}")
        print(f"✅ Files deleted: {result['files_deleted']}")
        print(f"❌ Files failed: {result['files_failed']}")
        print(f"💾 Total size deleted: {result['total_size_deleted']} bytes")
        
        if result['failed_deletions']:
            print("\n❌ Failed deletions:")
            for failure in result['failed_deletions'][:3]:  # Show first 3
                print(f"   - {failure['filename']}: {failure['error']}")
        
        return True
    else:
        print(f"❌ Folder deletion failed: {response.status_code}")
        print(f"Error: {response.text}")
        return False

def test_complete_cleanup_safety():
    """Test the safety checks for complete cleanup"""
    print("\n🚨 Testing Complete Cleanup Safety...")
    
    # Test without confirmation
    print("🧪 Test: Complete cleanup without confirmation (should fail)")
    response = requests.delete(f"{BASE_URL}/s3/cleanup")
    
    if response.status_code == 400:
        print("✅ Complete cleanup safety check working")
        print(f"   Message: {response.json()['detail']}")
        return True
    else:
        print(f"❌ Complete cleanup safety check failed: {response.status_code}")
        return False

def test_complete_cleanup():
    """Test complete S3 cleanup (DANGER!)"""
    print("\n🚨 DANGER: Testing Complete S3 Cleanup")
    print("⚠️  This will delete ALL data from ALL folders!")
    
    # Show current status
    total_files = show_s3_status()
    
    if total_files == 0:
        print("📂 All folders are already empty - nothing to clean")
        return True
    
    # Ask for user confirmation in a real scenario
    print(f"\n🗑️  Proceeding with complete cleanup of {total_files} files...")
    
    response = requests.delete(f"{BASE_URL}/s3/cleanup?confirm_cleanup=true")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Complete cleanup finished!")
        print(f"📊 Total files deleted: {result['total_files_deleted']}")
        print(f"💾 Total size freed: {result['total_size_deleted']} bytes")
        print(f"📂 Folders cleaned: {result['folders_cleaned']}")
        
        print("\n📋 Folder Results:")
        for folder, folder_result in result['folder_results'].items():
            status = folder_result['status']
            if status == "cleaned":
                print(f"   ✅ {folder}: {folder_result['files_deleted']} files deleted")
            elif status == "empty":
                print(f"   📂 {folder}: was already empty")
            elif status == "partial":
                print(f"   ⚠️  {folder}: {folder_result['files_deleted']}/{folder_result['files_found']} files deleted")
            else:
                print(f"   ❌ {folder}: error - {folder_result.get('error', 'unknown')}")
        
        return True
    else:
        print(f"❌ Complete cleanup failed: {response.status_code}")
        print(f"Error: {response.text}")
        return False

def main():
    """Run S3 cleanup tests"""
    print("🧪 TESTING S3 CLEANUP ENDPOINTS")
    print("=" * 50)
    
    # Check health first
    if not test_health_check():
        print("❌ API not healthy. Stopping tests.")
        return
    
    # Show initial status
    show_s3_status()
    
    # Test safety checks
    test_folder_deletion_safety()
    
    # Test folder deletion (use temp folder as it's safest)
    test_folder_deletion("temp")
    
    # Test complete cleanup safety
    test_complete_cleanup_safety()
    
    # Show final status
    print("\n📊 Final S3 Status:")
    show_s3_status()
    
    print("\n" + "=" * 50)
    print("🎉 S3 CLEANUP TESTING COMPLETED!")
    print("\n📋 Available Cleanup Endpoints:")
    print(f"🗑️  Delete specific folder: DELETE {BASE_URL}/s3/folders/{{folder}}?confirm=true")
    print(f"🚨 Delete everything: DELETE {BASE_URL}/s3/cleanup?confirm_cleanup=true")
    print("\n⚠️  Safety Features:")
    print("✅ Requires explicit confirmation parameters")
    print("✅ Validates folder names")
    print("✅ Provides detailed deletion reports")
    print("✅ Handles partial failures gracefully")
    
    print(f"\n🌐 API Documentation: {BASE_URL}/docs")

if __name__ == "__main__":
    main()
