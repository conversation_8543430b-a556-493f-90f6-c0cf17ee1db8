#!/usr/bin/env python3
"""
FastAPI Backend for Psychometrist Portal
Provides REST APIs for all AI-powered psychological assessment features
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os
import json
import shutil
from pathlib import Path
from datetime import datetime
import uuid
import logging
import asyncio
import time

app = FastAPI()

# Import our AI systems
from document_reader import read_document
from ai_document_analyzer import analyze_document
from openai_client import setup_openai_client
from ai_report_generator import generate_psychological_report
from s3_manager import s3_manager
from search_similar_reports import calculate_similarity_with_ai


# Set up logging
logging.basicConfig(
    filename="app.log",
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Initialize FastAPI app
app = FastAPI(
    title="Deanna's Psychometrist Portal API",
    description="AI-powered psychological assessment and report generation system",
    version="1.0.0"
)

# Add CORS middleware for web frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Create directories for uploads and outputs
UPLOAD_DIR = Path("uploads")
REPORTS_DIR = Path("reports")
TEMP_DIR = Path("temp")
STATIC_DIR = Path("static")

for directory in [UPLOAD_DIR, REPORTS_DIR, TEMP_DIR, STATIC_DIR]:
    directory.mkdir(exist_ok=True)

# Pydantic models for request/response
class DocumentAnalysisResponse(BaseModel):
    document_type: str
    patient_info: Dict[str, Any]
    assessment_details: Dict[str, Any]
    key_scores: List[str]
    main_findings: List[str]
    recommendations: List[str]
    confidence_level: str

class SimilarReportsRequest(BaseModel):
    """Request model for similar reports search"""
    target_s3_key: str
    filters: Optional[Dict[str, Any]] = None
    max_results: int = 10

# API Endpoints

@app.post("/upload")
async def upload_file(file: UploadFile = File(...), patient_id: str = None):
    """Upload a document file to S3 for analysis"""
    try:
        if not patient_id:
            raise HTTPException(status_code=400, detail="patient_id is required")

        # Read file content
        file_content = await file.read()

        # Upload to S3
        upload_result = s3_manager.upload_file(
            file_content=file_content,
            filename=file.filename,
            folder='uploads',
            patient_id=patient_id,
            metadata={
                'content-type': file.content_type or 'application/octet-stream',
                'uploaded-by': 'api-user'  # In production, get from auth
            }
        )

        return {
            "file_id": upload_result["file_id"],
            "filename": upload_result["filename"],
            "s3_key": upload_result["s3_key"],
            "s3_url": upload_result["s3_url"],
            "size": upload_result["size"],
            "upload_time": upload_result["upload_time"],
            "status": "uploaded_to_s3",
            "patient_id": patient_id
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"S3 upload failed: {str(e)}")

@app.post("/analyze-document")
async def analyze_single_document(s3_key: str):
    """Analyze a single document from S3 using AI"""
    try:
        # Download file from S3 to temporary location
        temp_file_path = s3_manager.download_file_to_temp(s3_key)

        try:
            # Analyze document with AI
            result = analyze_document(temp_file_path)

            if isinstance(result, dict) and "error" not in result:
                # Save analysis result to S3
                analysis_filename = f"analysis_{Path(s3_key).stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                s3_manager.upload_json_data(
                    data=result,
                    filename=analysis_filename,
                    folder='analysis_results'
                )

                return {
                    "status": "success",
                    "analysis": result,
                    "s3_key": s3_key,
                    "analysis_saved_to": f"analysis-results/{analysis_filename}",
                    "analyzed_at": datetime.now().isoformat()
                }
            else:
                raise HTTPException(status_code=500, detail=f"Analysis failed: {result}")

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis error: {str(e)}")

@app.post("/analysis-results")
async def get_document_analysis_results(patient_id: str):
    """Get comprehensive analysis results for all documents with patient ID"""
    try:
        # Get all files for the patient
        patient_files = s3_manager.list_files(folder='uploads', patient_id=patient_id)
        
        if not patient_files:
            return {
                "status": "success",
                "message": f"No files found for patient {patient_id}",
                "patient_id": patient_id,
                "documents_analyzed": 0,
                "results": []
            }

        # Set up AI for classification
        from document_analysis_results import classify_document_with_ai

        client, model, status = setup_openai_client()
        if status != "SUCCESS":
            raise HTTPException(status_code=500, detail=f"AI setup failed: {status}")

        # Initialize results storage
        analyses = []
        categories = {
            "Cognitive Tests": [],
            "Academic Tests": [],
            "Inventories": [],
            "Behavioral Observation Scales": [],
            "Background Info": [],
            "Forms/Consent": [],
            "Communication Logs": [],
            "Other": []
        }

        # Process each document
        for file_info in patient_files:
            try:
                # Download and read document
                temp_file_path = s3_manager.download_file_to_temp(file_info["s3_key"])

                try:
                    document_text = read_document(temp_file_path)

                    if not document_text.startswith("ERROR"):
                        # Classify with AI
                        classification, ai_status = classify_document_with_ai(
                            document_text[:3000],  # Limit text length
                            file_info["filename"],
                            client,
                            model
                        )

                        analysis = {
                            "filename": file_info["filename"],
                            "s3_key": file_info["s3_key"],
                            "classification": classification,
                            "status": ai_status
                        }

                        analyses.append(analysis)

                        # Add to category
                        if ai_status == "SUCCESS" and "classification" in analysis:
                            category = analysis["classification"].get("category", "Other")
                            if category in categories:
                                categories[category].append(analysis)
                            else:
                                categories["Other"].append(analysis)

                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

            except Exception as e:
                print(f"Error analyzing {file_info['filename']}: {e}")
                continue

        # Create summary
        successful_analyses = [a for a in analyses if a.get("status") == "SUCCESS"]
        
        # Calculate average confidence
        confidences = []
        for analysis in successful_analyses:
            if "classification" in analysis and "confidence" in analysis["classification"]:
                try:
                    conf = analysis["classification"]["confidence"]
                    # Handle both string and integer confidence values
                    if isinstance(conf, str):
                        conf = int(conf.replace("%", ""))
                    elif isinstance(conf, (int, float)):
                        conf = int(conf)
                    else:
                        continue
                    confidences.append(conf)
                except (ValueError, TypeError):
                    continue

        average_confidence = sum(confidences) / len(confidences) if confidences else 0

        # Count documents per category
        category_counts = {cat: len(docs) for cat, docs in categories.items()}

        return {
            "status": "success",
            "patient_id": patient_id,
            "results": {
                "categories": categories,
                "summary": {
                    "total_documents": len(patient_files),
                    "successful_analyses": len(successful_analyses),
                    "failed_analyses": len(patient_files) - len(successful_analyses),
                    "average_confidence": round(average_confidence, 1),
                    "categories_found": category_counts
                }
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis error: {str(e)}")

@app.post("/generate-report")
async def generate_report_from_s3(patient_id: str):
    """Generate a comprehensive psychological report directly from patient's documents"""
    try:
        set_report_status(patient_id, "in_progress")
        # Step 0: Get all files for the patient
        patient_files = await asyncio.to_thread(s3_manager.list_files, folder='uploads', patient_id=patient_id)

        if not patient_files:
            set_report_status(patient_id, "failed", error=f"No documents found for patient {patient_id}")
            raise HTTPException(
                status_code=404,
                detail=f"No documents found for patient {patient_id}"
            )

        # Step 1: Analyze all documents
        print("🔍 Step 1: Analyzing documents...")
        detailed_analyses = []
        patient_name = "Unknown_Patient"

        for i, file_info in enumerate(patient_files[:100], 1):
            print(f"   📋 Analyzing {i}/{min(len(patient_files), 100)}: {file_info['filename']}")

            try:
                # Download document
                temp_file_path = await asyncio.to_thread(s3_manager.download_file_to_temp, file_info["s3_key"])

                try:
                    # Analyze document
                    analysis_result = await asyncio.to_thread(analyze_document, temp_file_path)

                    if isinstance(analysis_result, dict) and "error" not in analysis_result:
                        detailed_analyses.append(analysis_result)

                        if patient_name == "Unknown_Patient" and "patient_info" in analysis_result:
                            patient_name = analysis_result["patient_info"].get("name", "Unknown_Patient")

                        print(f"      ✅ Success: {analysis_result.get('document_type', 'Unknown type')}")
                    else:
                        print(f"      ❌ Failed to analyze: {file_info['filename']}")

                finally:
                    if os.path.exists(temp_file_path):
                        await asyncio.to_thread(os.unlink, temp_file_path)

            except Exception as e:
                print(f"      ❌ Error analyzing {file_info['filename']}: {e}")
                continue

        if not detailed_analyses:
            set_report_status(patient_id, "failed", error="No documents could be analyzed successfully")
            raise HTTPException(status_code=500, detail="No documents could be analyzed successfully")

        print(f"✅ Successfully analyzed {len(detailed_analyses)} documents")

        # Step 2: Generate comprehensive psychological report
        print("📝 Step 2: Generating comprehensive report with AI...")
        report_result = await asyncio.to_thread(
            generate_psychological_report,
            detailed_analyses,
            s3_manager=s3_manager,
            s3_folder='reports',
            patient_id=patient_id
        )

        if report_result.get("status") != "SUCCESS":
            set_report_status(patient_id, "failed", error=f"Report generation failed: {report_result.get('error')}")
            raise HTTPException(status_code=500, detail=f"Report generation failed: {report_result.get('error')}")

        # Step 3: Save report to S3
        print("💾 Step 3: Saving report to S3...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{safe_name}_{timestamp}_psychological_report.txt"

        upload_result = await asyncio.to_thread(
            s3_manager.upload_file,
            file_content=report_result["report"].encode('utf-8'),
            filename=filename,
            folder='reports',
            metadata={
                'patient-id': patient_id,
                'patient-name': patient_name,
                'report-type': 'psychological-assessment',
                'documents-analyzed': str(len(detailed_analyses)),
                'generated-by': 'direct-s3-workflow'
            }
        )

        # Step 4: Save analysis results to S3
        print("📊 Step 4: Saving analysis results to S3...")
        analysis_filename = f"{safe_name}_{timestamp}_analysis_data.json"

        analysis_upload = await asyncio.to_thread(
            s3_manager.upload_json_data,
            data={
                "patient_id": patient_id,
                "patient_name": patient_name,
                "timestamp": datetime.now().isoformat(),
                "documents_analyzed": len(detailed_analyses),
                "total_documents": len(patient_files),
                "detailed_analyses": detailed_analyses,
                "report_s3_key": upload_result["s3_key"]
            },
            filename=analysis_filename,
            folder='analysis_results'
        )

        # Step 5: Get JSON report S3 key from report_result
        report_json_s3_key = report_result.get("json_s3_key") or report_result.get("json_path")
        report_json_s3_url = None
        if report_json_s3_key:
            report_json_s3_url = f"s3://{s3_manager.bucket_name}/{report_json_s3_key}"

        print("✅ Report generation completed successfully!")
        set_report_status(patient_id, "ready")

        return {
            "status": "success",
            "patient_id": patient_id,
            "patient_name": patient_name,
            "documents_found": len(patient_files),
            "documents_analyzed": len(detailed_analyses),
            "report": report_result["report"],
            "report_s3_key": upload_result["s3_key"],
            "report_s3_url": upload_result["s3_url"],
            "report_filename": filename,
            "report_json_s3_key": report_json_s3_key,
            "report_json_s3_url": report_json_s3_url,
            "analysis_s3_key": analysis_upload["s3_key"],
            "analysis_s3_url": analysis_upload["s3_url"],
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        set_report_status(patient_id, "failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Report generation error: {str(e)}")

@app.post("/generate-cheatsheet")
async def generate_cheatsheet_from_s3():
    """Generate cheatsheet templates directly from S3 reports using existing AI"""
    try:
        print("🚀 Starting cheatsheet generation from S3 reports...")

        # Step 1: Get all reports from S3
        print("📋 Step 1: Finding reports in S3...")
        report_files = s3_manager.list_files(folder="reports")

        if not report_files:
            raise HTTPException(status_code=404, detail="No reports found in S3")

        print(f"📄 Found {len(report_files)} reports")

        # Step 2: Download and read reports
        print("📖 Step 2: Reading report contents...")
        report_contents = []

        for i, report_file in enumerate(report_files[:10], 1):  # Limit to 10 for performance
            if "_psychological_report" in report_file["filename"] or "_report" in report_file["filename"]:
                print(f"   📋 Reading {i}: {report_file['filename']}")

                try:
                    # Download report content
                    content_bytes = s3_manager.download_file(report_file["s3_key"])
                    content = content_bytes.decode('utf-8')

                    if len(content.strip()) > 100:  # Only include substantial reports
                        report_contents.append({
                            "filename": report_file["filename"],
                            "content": content,
                            "s3_key": report_file["s3_key"]
                        })
                        print(f"      ✅ Added ({len(content)} chars)")
                    else:
                        print(f"      ⏭️  Skipped (too short)")

                except Exception as e:
                    print(f"      ❌ Error reading {report_file['filename']}: {e}")
                    continue

        if not report_contents:
            raise HTTPException(status_code=404, detail="No readable reports found")

        print(f"✅ Successfully read {len(report_contents)} reports")

        # Step 3: Set up AI for template extraction
        print("🧠 Step 3: Setting up AI for template extraction...")
        client, model, status = setup_openai_client()
        
        if status != "SUCCESS":
            raise HTTPException(status_code=500, detail=f"AI setup failed: {status}")
        
        print(f"✅ OpenAI ready (Model: {model})")

        # Step 4: Extract templates using AI
        print("🎯 Step 4: Extracting templates with AI...")

        # Create a comprehensive prompt for template extraction
        all_reports_text = "\n\n--- REPORT SEPARATOR ---\n\n".join([r["content"] for r in report_contents])

        template_prompt = f"""
You are an expert clinical psychologist analyzing multiple psychological reports to create reusable templates.

REPORTS TO ANALYZE:
{all_reports_text[:15000]}

Create a comprehensive cheatsheet with reusable templates. Extract common patterns and create templates with placeholders.

Return a JSON response with these categories:

{{
    "background_templates": [
        "{{Patient_Name}}, a {{Age}} y/o {{Gender}}, was referred for...",
        "{{Patient_Name}} presented with {{Primary_Concern}} affecting {{Area_of_Impact}}..."
    ],
    "assessment_templates": [
        "Cognitive assessment reveals {{Test_Name}} FSIQ of {{Score}} ({{Percentile}})",
        "Behavioral assessment indicates {{Level}} {{Concern}} ({{Scale}}: {{Score}})"
    ],
    "findings_templates": [
        "Results indicate {{Level}} {{Ability}} in {{Domain}}",
        "{{Patient_Name}} demonstrates {{Strength}} in {{Area}} with concerns in {{Weakness_Area}}"
    ],
    "recommendation_templates": [
        "Recommend {{Frequency}} {{Treatment_Type}} to address {{Target_Issue}}",
        "Educational accommodations including {{Accommodation_Type}} for {{Subject_Area}}"
    ],
    "score_templates": [
        "{{Test_Name}}: {{Subtest}} {{Score}} ({{Percentile}}) - {{Interpretation}}",
        "Standard Score: {{Score}} ({{Range}}) indicating {{Level}} performance"
    ],
    "common_phrases": [
        "clinically significant",
        "within normal limits",
        "at-risk levels"
    ]
}}

Focus on creating practical, reusable templates that psychologists can use. Use clear placeholders like {{Patient_Name}}, {{Score}}, {{Test_Name}}, etc.

Return ONLY valid JSON, no other text.
"""

        try:
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": template_prompt}
                ],
                temperature=0.1,  # Low temperature for consistent results
                max_tokens=2000
            )

            ai_response = response.choices[0].message.content

            # Try to parse the JSON response
            try:
                cheatsheet_data = json.loads(ai_response)
                print("✅ AI successfully extracted templates")
            except json.JSONDecodeError:
                # Try to extract JSON from response
                import re
                json_match = re.search(r'(\{.*\})', ai_response, re.DOTALL)
                if json_match:
                    cheatsheet_data = json.loads(json_match.group(1))
                    print("✅ AI templates extracted (with cleanup)")
                else:
                    raise Exception("Could not parse AI response as JSON")

        except Exception as e:
            print(f"❌ AI template extraction failed: {e}")
            # Fallback: Create basic templates
            cheatsheet_data = {
                "background_templates": [
                    "{Patient_Name}, a {Age} y/o {Gender}, was referred for psychological assessment.",
                    "{Patient_Name} presented with {Primary_Concern} affecting daily functioning."
                ],
                "assessment_templates": [
                    "Cognitive assessment reveals {Test_Name} FSIQ of {Score} ({Percentile}).",
                    "Behavioral assessment indicates {Level} {Concern} ({Scale}: {Score})."
                ],
                "findings_templates": [
                    "Results indicate {Level} {Ability} in {Domain}.",
                    "{Patient_Name} demonstrates strengths in {Area} with concerns in {Weakness_Area}."
                ],
                "recommendation_templates": [
                    "Recommend {Frequency} {Treatment_Type} to address {Target_Issue}.",
                    "Educational accommodations including {Accommodation_Type} for {Subject_Area}."
                ],
                "score_templates": [
                    "{Test_Name}: {Subtest} {Score} ({Percentile}) - {Interpretation}",
                    "Standard Score: {Score} ({Range}) indicating {Level} performance"
                ],
                "common_phrases": [
                    "clinically significant", "within normal limits", "at-risk levels"
                ]
            }
            print("✅ Using fallback templates")

        # Step 5: Add metadata and save to S3
        print("💾 Step 5: Saving cheatsheet to S3...")

        # Add metadata to cheatsheet
        final_cheatsheet = {
            "generated_at": datetime.now().isoformat(),
            "reports_analyzed": len(report_contents),
            "source_reports": [r["filename"] for r in report_contents],
            "ai_model": model,
            "templates": cheatsheet_data
        }

        # Save to S3
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ai_cheatsheet_{timestamp}.json"

        upload_result = s3_manager.upload_json_data(
            data=final_cheatsheet,
            filename=filename,
            folder='cheatsheets'
        )

        print("✅ Cheatsheet generation completed successfully!")

        return {
            "status": "success",
            "cheatsheet": final_cheatsheet,
            "reports_analyzed": len(report_contents),
            "total_templates": sum(len(templates) if isinstance(templates, list) else 0
                                 for templates in cheatsheet_data.values()),
            "s3_key": upload_result["s3_key"],
            "s3_url": upload_result["s3_url"],
            "filename": filename,
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"S3 cheatsheet generation error: {str(e)}")

@app.post("/search-similar")
async def search_similar_by_ai(request: SimilarReportsRequest):
    """Search for similar reports using AI-powered matching with filters"""
    try:
        print(f"🔍 Starting AI similarity search for: {request.target_s3_key}")

        # Step 1: Download target report from S3
        print("📋 Step 1: Reading target report...")
        target_content_bytes = s3_manager.download_file(request.target_s3_key)
        target_content = target_content_bytes.decode('utf-8')

        # Step 2: Get all reports from S3
        print("📂 Step 2: Finding comparison reports...")
        report_files = s3_manager.list_files(folder="reports")

        if not report_files:
            raise HTTPException(status_code=404, detail="No reports found in S3")

        comparison_reports = [f for f in report_files if f["s3_key"] != request.target_s3_key]
        print(f"📄 Found {len(comparison_reports)} reports to compare")

        # Step 3: Set up AI
        print("🔧 Setting up OpenAI...")
        client, model, status = setup_openai_client()
        
        if status != "SUCCESS":
            raise HTTPException(status_code=500, detail=f"AI setup failed: {status}")
        
        print(f"✅ OpenAI ready (Model: {model})")

        # Step 4: Calculate similarities with filters
        print("🧠 Step 4: Calculating similarities...")
        similarities = []

        for i, report_file in enumerate(comparison_reports[:20], 1):  # Limit to 20 for performance
            if "_psychological_report" in report_file["filename"] or "_report" in report_file["filename"]:
                print(f"   📋 Comparing {i}: {report_file['filename']}")

                try:
                    # Download comparison report
                    comparison_bytes = s3_manager.download_file(report_file["s3_key"])
                    comparison_content = comparison_bytes.decode('utf-8')

                    # Extract metadata for filtering
                    comparison_metadata = extract_metadata_from_text(comparison_content, report_file["filename"])

                    # Apply filters if provided
                    if request.filters and not passes_metadata_filters(comparison_metadata, request.filters):
                        print(f"   ⏭️  Skipped (doesn't match filters)")
                        continue

                    # Calculate similarity with AI
                    similarity, ai_status = calculate_similarity_with_ai(
                        target_content[:2000],  # Limit length for faster processing
                        comparison_content[:2000],
                        client,
                        model
                    )

                    if ai_status == "SUCCESS":
                        # Add metadata and file info
                        similarity["s3_key"] = report_file["s3_key"]
                        similarity["filename"] = report_file["filename"]
                        similarity["last_modified"] = report_file.get("last_modified", "")
                        similarity["size"] = report_file.get("size", 0)
                        similarity["metadata"] = comparison_metadata
                        similarities.append(similarity)
                        
                        overall_sim = similarity.get("overall_similarity", 0)
                        print(f"   ✅ Similarity: {overall_sim}%")
                    else:
                        print(f"   ❌ Failed to calculate similarity")

                except Exception as e:
                    print(f"   ❌ Error processing {report_file['filename']}: {e}")
                    continue

        # Step 5: Sort by similarity and return top results
        print("🏆 Step 5: Ranking results...")
        similarities.sort(key=lambda x: int(x.get("overall_similarity", 0)), reverse=True)
        top_results = similarities[:request.max_results]

        result = {
            "target_s3_key": request.target_s3_key,
            "target_filename": Path(request.target_s3_key).name,
            "similar_reports": top_results,
            "total_compared": len(similarities),
            "filters_applied": request.filters or {},
            "search_method": "ai_based"
        }

        print(f"✅ Found {len(top_results)} similar reports")

        return {
            "status": "success",
            "search_results": result,
            "searched_at": datetime.now().isoformat()
        }

    except Exception as e:
        if "NoSuchKey" in str(e):
            raise HTTPException(status_code=404, detail="Target report not found in S3")
        raise HTTPException(status_code=500, detail=f"AI similarity search error: {str(e)}")

@app.post("/complete-workflow")
async def run_complete_workflow(patient_id: str):
    """Run the complete psychometrist portal workflow using S3"""
    try:
        print(f"🚀 Starting S3-based complete workflow for: {patient_id}")

        # Step 1: Get all patient documents from S3
        print("📋 Step 1: Finding patient documents in S3...")
        all_files = s3_manager.list_files(folder="uploads")
        patient_files = [f for f in all_files if patient_id in f["s3_key"]]

        if not patient_files:
            raise HTTPException(status_code=404, detail=f"No documents found for patient: {patient_id}")

        print(f"📄 Found {len(patient_files)} documents")

        # Step 2: Analyze each document with AI
        print("🧠 Step 2: Analyzing documents with AI...")
        detailed_analyses = []
        patient_name = "Unknown_Patient"

        for i, file_info in enumerate(patient_files[:10], 1):  # Limit to 10 for performance
            print(f"   📋 Analyzing {i}/{min(len(patient_files), 10)}: {file_info['filename']}")

            try:
                # Download and analyze document
                temp_file_path = s3_manager.download_file_to_temp(file_info["s3_key"])

                try:
                    from ai_document_analyzer import analyze_document
                    analysis_result = analyze_document(temp_file_path)

                    if isinstance(analysis_result, dict) and "error" not in analysis_result:
                        detailed_analyses.append(analysis_result)

                        # Extract patient name from first successful analysis
                        if patient_name == "Unknown_Patient" and "patient_info" in analysis_result:
                            patient_name = analysis_result["patient_info"].get("name", "Unknown_Patient")

                        print(f"      ✅ Success: {analysis_result.get('document_type', 'Unknown type')}")
                    else:
                        print(f"      ❌ Failed to analyze: {file_info['filename']}")

                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

            except Exception as e:
                print(f"      ❌ Error analyzing {file_info['filename']}: {e}")
                continue

        if not detailed_analyses:
            raise HTTPException(status_code=500, detail="No documents could be analyzed successfully")

        print(f"✅ Successfully analyzed {len(detailed_analyses)} documents")

        # Step 3: Generate comprehensive psychological report
        print("📝 Step 3: Generating comprehensive report...")

        report_result = generate_psychological_report(detailed_analyses)

        if report_result.get("status") != "SUCCESS":
            raise HTTPException(status_code=500, detail=f"Report generation failed: {report_result.get('error')}")

        # Step 4: Save report to S3
        print("💾 Step 4: Saving report to S3...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        report_filename = f"{safe_name}_{timestamp}_comprehensive_report.txt"

        report_upload = s3_manager.upload_file(
            file_content=report_result["report"].encode('utf-8'),
            filename=report_filename,
            folder='reports',
            metadata={
                'patient-id': patient_id,
                'patient-name': patient_name,
                'report-type': 'comprehensive-assessment',
                'documents-analyzed': str(len(detailed_analyses)),
                'generated-by': 'complete-workflow'
            }
        )

        # Step 5: Save analysis results to S3
        print("📊 Step 5: Saving analysis results to S3...")

        analysis_filename = f"{safe_name}_{timestamp}_analysis_results.json"
        analysis_upload = s3_manager.upload_json_data(
            data={
                "patient_id": patient_id,
                "patient_name": patient_name,
                "timestamp": datetime.now().isoformat(),
                "documents_analyzed": len(detailed_analyses),
                "total_documents": len(patient_files),
                "detailed_analyses": detailed_analyses,
                "workflow_status": "SUCCESS"
            },
            filename=analysis_filename,
            folder='analysis_results'
        )

        # Create workflow summary
        workflow_summary = {
            "patient_id": patient_id,
            "patient_name": patient_name,
            "timestamp": datetime.now().isoformat(),
            "documents_found": len(patient_files),
            "documents_analyzed": len(detailed_analyses),
            "report_s3_key": report_upload["s3_key"],
            "report_s3_url": report_upload["s3_url"],
            "analysis_s3_key": analysis_upload["s3_key"],
            "analysis_s3_url": analysis_upload["s3_url"],
            "status": "SUCCESS"
        }

        print("✅ Complete workflow finished successfully!")

        return {
            "status": "success",
            "workflow_result": workflow_summary,
            "completed_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"S3 workflow error: {str(e)}")

@app.get("/reports")
async def list_reports():
    """List all generated reports from S3"""
    try:
        files = s3_manager.list_files(folder="reports")

        reports = []
        for file_info in files:
            if "_psychological_report" in file_info["filename"]:
                reports.append({
                    "filename": file_info["filename"],
                    "s3_key": file_info["s3_key"],
                    "size": file_info["size"],
                    "last_modified": file_info["last_modified"],
                    "metadata": file_info.get("metadata", {})
                })

        return {
            "status": "success",
            "reports": sorted(reports, key=lambda x: x["last_modified"], reverse=True),
            "total_reports": len(reports)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing S3 reports: {str(e)}")

@app.get("/reports/{s3_key:path}")
async def get_report_content(s3_key: str):
    """Get the content of a specific report from S3"""
    try:
        # Download report content from S3
        content_bytes = s3_manager.download_file(s3_key)
        content = content_bytes.decode('utf-8')

        # Get metadata
        metadata = s3_manager.get_file_metadata(s3_key)

        return {
            "status": "success",
            "s3_key": s3_key,
            "filename": Path(s3_key).name,
            "content": content,
            "size": metadata["size"],
            "last_modified": metadata["last_modified"],
            "retrieved_at": datetime.now().isoformat()
        }

    except Exception as e:
        if "NoSuchKey" in str(e):
            raise HTTPException(status_code=404, detail="Report not found in S3")
        raise HTTPException(status_code=500, detail=f"Error retrieving S3 report: {str(e)}")

@app.delete("/reports/{s3_key:path}")
async def delete_report(s3_key: str):
    """Delete a specific report from S3"""
    try:
        success = s3_manager.delete_file(s3_key)

        if success:
            return {
                "status": "success",
                "message": f"Report {Path(s3_key).name} deleted successfully from S3",
                "s3_key": s3_key,
                "deleted_at": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="Delete operation failed")

    except Exception as e:
        if "NoSuchKey" in str(e):
            raise HTTPException(status_code=404, detail="Report not found in S3")
        raise HTTPException(status_code=500, detail=f"Error deleting S3 report: {str(e)}")

@app.get("/s3/files")
async def list_s3_files(folder: str = "uploads", patient_id: Optional[str] = None):
    """List files in S3 folder"""
    try:
        files = s3_manager.list_files(folder=folder, patient_id=patient_id)
        return {
            "status": "success",
            "folder": folder,
            "patient_id": patient_id,
            "files": files,
            "total_files": len(files)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing S3 files: {str(e)}")

@app.get("/s3/download/{s3_key:path}")
async def get_s3_file_url(s3_key: str, expiration: int = 3600):
    """Generate presigned URL for S3 file download"""
    try:
        url = s3_manager.generate_presigned_url(s3_key, expiration=expiration)
        return {
            "status": "success",
            "s3_key": s3_key,
            "download_url": url,
            "expires_in": expiration,
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating download URL: {str(e)}")

@app.delete("/s3/files/{s3_key:path}")
async def delete_s3_file(s3_key: str):
    """Delete file from S3"""
    try:
        success = s3_manager.delete_file(s3_key)
        if success:
            return {
                "status": "success",
                "message": f"File {s3_key} deleted successfully",
                "deleted_at": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="Delete operation failed")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting S3 file: {str(e)}")

@app.delete("/s3/folders/{folder}")
async def delete_s3_folder(folder: str, confirm: bool = False):
    """Delete all files in a specific S3 folder"""
    try:
        # Safety check - require confirmation
        if not confirm:
            raise HTTPException(
                status_code=400,
                detail="This operation will delete ALL files in the folder. Add '?confirm=true' to proceed."
            )

        # Validate folder name
        valid_folders = ["uploads", "reports", "cheatsheets", "analysis_results", "temp"]
        if folder not in valid_folders:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid folder. Must be one of: {', '.join(valid_folders)}"
            )

        print(f"🗑️  Starting deletion of all files in S3 folder: {folder}")

        # Get all files in the folder
        files = s3_manager.list_files(folder=folder)

        if not files:
            return {
                "status": "success",
                "message": f"Folder '{folder}' is already empty",
                "files_deleted": 0,
                "deleted_at": datetime.now().isoformat()
            }

        print(f"📄 Found {len(files)} files to delete")

        # Delete each file
        deleted_files = []
        failed_deletions = []

        for i, file_info in enumerate(files, 1):
            print(f"   🗑️  Deleting {i}/{len(files)}: {file_info['filename']}")

            try:
                success = s3_manager.delete_file(file_info["s3_key"])
                if success:
                    deleted_files.append({
                        "filename": file_info["filename"],
                        "s3_key": file_info["s3_key"],
                        "size": file_info["size"]
                    })
                    print(f"      ✅ Deleted")
                else:
                    failed_deletions.append({
                        "filename": file_info["filename"],
                        "s3_key": file_info["s3_key"],
                        "error": "Delete operation returned false"
                    })
                    print(f"      ❌ Failed")

            except Exception as e:
                failed_deletions.append({
                    "filename": file_info["filename"],
                    "s3_key": file_info["s3_key"],
                    "error": str(e)
                })
                print(f"      ❌ Error: {e}")

        # Calculate total size deleted
        total_size_deleted = sum(file["size"] for file in deleted_files)

        result = {
            "status": "success" if not failed_deletions else "partial_success",
            "folder": folder,
            "files_found": len(files),
            "files_deleted": len(deleted_files),
            "files_failed": len(failed_deletions),
            "total_size_deleted": total_size_deleted,
            "deleted_files": deleted_files,
            "failed_deletions": failed_deletions,
            "deleted_at": datetime.now().isoformat()
        }

        if failed_deletions:
            print(f"⚠️  Completed with {len(failed_deletions)} failures")
        else:
            print(f"✅ Successfully deleted all {len(deleted_files)} files")

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting S3 folder: {str(e)}")

@app.delete("/s3/cleanup")
async def cleanup_all_s3_data(confirm_cleanup: bool = False):
    """Delete ALL data from ALL S3 folders (DANGER: Use with extreme caution!)"""
    try:
        # Extra safety check
        if not confirm_cleanup:
            raise HTTPException(
                status_code=400,
                detail="⚠️  DANGER: This will delete ALL data from ALL folders! Add '?confirm_cleanup=true' to proceed."
            )

        print("🚨 STARTING COMPLETE S3 CLEANUP - DELETING ALL DATA!")

        folders_to_clean = ["uploads", "reports", "cheatsheets", "analysis_results", "temp"]
        cleanup_results = {}
        total_files_deleted = 0
        total_size_deleted = 0

        for folder in folders_to_clean:
            print(f"\n🗑️  Cleaning folder: {folder}")

            try:
                files = s3_manager.list_files(folder=folder)

                if not files:
                    cleanup_results[folder] = {
                        "status": "empty",
                        "files_deleted": 0,
                        "size_deleted": 0
                    }
                    print(f"   📂 Folder '{folder}' is already empty")
                    continue

                deleted_count = 0
                deleted_size = 0
                failed_count = 0

                for file_info in files:
                    try:
                        success = s3_manager.delete_file(file_info["s3_key"])
                        if success:
                            deleted_count += 1
                            deleted_size += file_info["size"]
                        else:
                            failed_count += 1
                    except Exception as e:
                        failed_count += 1
                        print(f"   ❌ Failed to delete {file_info['filename']}: {e}")

                cleanup_results[folder] = {
                    "status": "cleaned" if failed_count == 0 else "partial",
                    "files_found": len(files),
                    "files_deleted": deleted_count,
                    "files_failed": failed_count,
                    "size_deleted": deleted_size
                }

                total_files_deleted += deleted_count
                total_size_deleted += deleted_size

                print(f"   ✅ Deleted {deleted_count}/{len(files)} files ({deleted_size} bytes)")

            except Exception as e:
                cleanup_results[folder] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"   ❌ Error cleaning folder '{folder}': {e}")

        print(f"\n🎯 CLEANUP COMPLETE!")
        print(f"📊 Total files deleted: {total_files_deleted}")
        print(f"💾 Total size freed: {total_size_deleted} bytes")

        return {
            "status": "success",
            "message": "Complete S3 cleanup performed",
            "folders_cleaned": len(folders_to_clean),
            "total_files_deleted": total_files_deleted,
            "total_size_deleted": total_size_deleted,
            "folder_results": cleanup_results,
            "cleaned_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error during S3 cleanup: {str(e)}")

@app.get("/health")
async def health_check():
    """Detailed health check with system status"""
    try:
        # Check if AI system is working
        client, model, ai_status = setup_openai_client()

        # Check S3 connection
        s3_health = s3_manager.health_check()

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "ai_system": {
                "openai_status": ai_status,
                "model": model if ai_status == "SUCCESS" else None
            },
            "s3_system": s3_health,
            "version": "1.0.0"
        }

    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# Helper functions for similarity search
def extract_metadata_from_text(content: str, filename: str) -> Dict[str, Any]:
    """Extract metadata from report text using regex patterns"""
    import re

    metadata = {
        "filename": filename,
        "age": None,
        "gender": None,
        "test_types": [],
        "diagnoses": [],
        "keywords": []
    }

    # Extract age
    age_patterns = [
        r'(\d+)[-\s]year[-\s]old',
        r'(\d+)\s*y/o',
        r'age[:\s]+(\d+)',
        r'(\d+)\s*years?\s*of\s*age'
    ]

    for pattern in age_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            try:
                metadata["age"] = int(match.group(1))
                break
            except (ValueError, IndexError):
                continue

    # Extract gender
    if re.search(r'\bmale\b(?!\s*female)', content, re.IGNORECASE):
        metadata["gender"] = "Male"
    elif re.search(r'\bfemale\b', content, re.IGNORECASE):
        metadata["gender"] = "Female"

    # Extract test types
    test_patterns = [
        r'\bWISC[-\s]?V?\b', r'\bWAIS[-\s]?IV?\b', r'\bBASC[-\s]?3?\b',
        r'\bWIAT[-\s]?III?\b', r'\bConners?\b', r'\bMASC\b', r'\bCDI\b',
        r'\bVINELAND\b', r'\bADOS\b', r'\bADI[-\s]?R\b'
    ]

    for pattern in test_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            test_name = pattern.replace(r'\b', '').replace('[-\s]?', '-').replace('\\', '')
            metadata["test_types"].append(test_name)

    # Extract common diagnoses/conditions
    diagnosis_patterns = [
        r'\banxiety\b', r'\bdepression\b', r'\bADHD\b', r'\bautism\b',
        r'\bPTSD\b', r'\bOCD\b', r'\blearning\s+disorder\b',
        r'\bintellectual\s+disability\b', r'\bbipolar\b'
    ]

    for pattern in diagnosis_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            diagnosis = pattern.replace(r'\b', '').replace('\\', '').replace('s+', ' ')
            metadata["diagnoses"].append(diagnosis)

    # Extract key clinical terms
    keyword_patterns = [
        r'\bclinically\s+significant\b', r'\bwithin\s+normal\s+limits\b',
        r'\bat[-\s]risk\b', r'\baverage\s+range\b', r'\bhigh\s+average\b',
        r'\blow\s+average\b', r'\bsuperior\b', r'\bimpaired\b'
    ]

    for pattern in keyword_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            keyword = pattern.replace(r'\b', '').replace('\\', '').replace('s+', ' ').replace('[-\s]', ' ')
            metadata["keywords"].append(keyword)

    return metadata

def calculate_metadata_similarity(target_meta: Dict, comparison_meta: Dict, target_content: str, comparison_content: str) -> Dict[str, Any]:
    """Calculate similarity score based on metadata and content"""

    scores = {
        "age_score": 0,
        "gender_score": 0,
        "test_score": 0,
        "diagnosis_score": 0,
        "keyword_score": 0,
        "content_score": 0
    }

    # Age similarity (within 2 years = 100%, within 5 years = 50%, etc.)
    if target_meta.get("age") and comparison_meta.get("age"):
        age_diff = abs(target_meta["age"] - comparison_meta["age"])
        if age_diff == 0:
            scores["age_score"] = 100
        elif age_diff <= 2:
            scores["age_score"] = 90
        elif age_diff <= 5:
            scores["age_score"] = 70
        elif age_diff <= 10:
            scores["age_score"] = 40
        else:
            scores["age_score"] = 10

    # Gender match
    if target_meta.get("gender") and comparison_meta.get("gender"):
        if target_meta["gender"] == comparison_meta["gender"]:
            scores["gender_score"] = 100
        else:
            scores["gender_score"] = 0

    # Test types overlap
    target_tests = set(target_meta.get("test_types", []))
    comparison_tests = set(comparison_meta.get("test_types", []))
    if target_tests and comparison_tests:
        overlap = len(target_tests.intersection(comparison_tests))
        total = len(target_tests.union(comparison_tests))
        scores["test_score"] = int((overlap / total) * 100) if total > 0 else 0

    # Diagnosis overlap
    target_diagnoses = set(target_meta.get("diagnoses", []))
    comparison_diagnoses = set(comparison_meta.get("diagnoses", []))
    if target_diagnoses and comparison_diagnoses:
        overlap = len(target_diagnoses.intersection(comparison_diagnoses))
        total = len(target_diagnoses.union(comparison_diagnoses))
        scores["diagnosis_score"] = int((overlap / total) * 100) if total > 0 else 0

    # Keyword overlap
    target_keywords = set(target_meta.get("keywords", []))
    comparison_keywords = set(comparison_meta.get("keywords", []))
    if target_keywords and comparison_keywords:
        overlap = len(target_keywords.intersection(comparison_keywords))
        total = len(target_keywords.union(comparison_keywords))
        scores["keyword_score"] = int((overlap / total) * 100) if total > 0 else 0

    # Simple content similarity (word overlap)
    target_words = set(target_content.lower().split()[:500])  # First 500 words
    comparison_words = set(comparison_content.lower().split()[:500])
    if target_words and comparison_words:
        overlap = len(target_words.intersection(comparison_words))
        total = len(target_words.union(comparison_words))
        scores["content_score"] = int((overlap / total) * 100) if total > 0 else 0

    # Calculate weighted total score
    weights = {
        "age_score": 0.25,
        "gender_score": 0.15,
        "test_score": 0.20,
        "diagnosis_score": 0.25,
        "keyword_score": 0.10,
        "content_score": 0.05
    }

    total_score = sum(scores[key] * weights[key] for key in scores.keys())
    scores["total_score"] = int(total_score)

    return scores

def passes_metadata_filters(metadata: Dict, filters: Dict) -> bool:
    """Check if metadata passes the specified filters"""
    if not filters:
        return True

    # Age filters
    if "age_min" in filters and metadata.get("age"):
        if metadata["age"] < filters["age_min"]:
            return False

    if "age_max" in filters and metadata.get("age"):
        if metadata["age"] > filters["age_max"]:
            return False

    # Gender filter
    if "gender" in filters and filters["gender"] != "All":
        if metadata.get("gender") != filters["gender"]:
            return False

    # Test type filter
    if "test_types" in filters and filters["test_types"]:
        required_tests = set(filters["test_types"])
        available_tests = set(metadata.get("test_types", []))
        if not required_tests.intersection(available_tests):
            return False

    # Diagnosis filter
    if "diagnoses" in filters and filters["diagnoses"]:
        required_diagnoses = set(filters["diagnoses"])
        available_diagnoses = set(metadata.get("diagnoses", []))
        if not required_diagnoses.intersection(available_diagnoses):
            return False

    return True

# Root endpoint to serve the main page
@app.get("/")
async def read_root():
    return FileResponse("static/index.html")

def set_report_status(patient_id, status, error=None):
    status_data = {"status": status, "timestamp": time.time()}
    if error:
        status_data["error"] = error
    with open(f"status_{patient_id}.json", "w") as f:
        json.dump(status_data, f)

def get_report_status(patient_id):
    try:
        with open(f"status_{patient_id}.json") as f:
            data = json.load(f)
        if data["status"] == "in_progress":
            now = time.time()
            # 2 hours = 7200 seconds
            if now - data.get("timestamp", now) > 7200:
                return {"status": "failed", "error": "Timed out or interrupted", "timestamp": data.get("timestamp")}
        return data
    except FileNotFoundError:
        return {"status": "not_started"}

@app.get("/report-status")
def report_status(patient_id: str):
    return get_report_status(patient_id)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=3000,
        workers=4,
        log_config=None
    )