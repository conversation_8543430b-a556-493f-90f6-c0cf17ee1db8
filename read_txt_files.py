import os

SAMPLE_REPORTS_DIR = 'sample_reports'

def read_first_lines(txt_path, num_lines=20):
    with open(txt_path, 'r', encoding='utf-8') as txt_file:
        lines = [next(txt_file) for _ in range(num_lines)]
    return lines

def extract_section_headings(txt_path):
    with open(txt_path, 'r', encoding='utf-8') as txt_file:
        lines = txt_file.readlines()
    headings = [line.strip() for line in lines if line.strip().isupper() or line.strip().endswith(':')]
    return headings

def main():
    for filename in os.listdir(SAMPLE_REPORTS_DIR):
        if filename.lower().endswith('.txt'):
            txt_path = os.path.join(SAMPLE_REPORTS_DIR, filename)
            print(f'\n--- {filename} ---')
            print('First 20 lines:')
            first_lines = read_first_lines(txt_path)
            for line in first_lines:
                print(line.strip())
            print('\nSection Headings:')
            headings = extract_section_headings(txt_path)
            for heading in headings:
                print(heading)

if __name__ == '__main__':
    main() 